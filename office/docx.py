import streamlit as st
import tempfile
import os
import re
import docx
from transformers import M2M100ForConditionalGeneration, M2M100Tokenizer
import torch
import nltk
import logging
import zipfile
import xml.etree.ElementTree as ET
from io import BytesIO
import json
from difflib import get_close_matches
from langdetect import detect

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# -------------------------------
# CPU Management Functions
# -------------------------------
def clear_cpu_cache():
    pass  

def get_cpu_memory_info():
    return 0, 0  

def check_cpu_memory():
    return True  

# -------------------------------
# Knowledge Base Loader
# -------------------------------
@st.cache_resource
def load_combined_kb(files):
    combined_kb = {}
    def normalize_key(s: str) -> str:
        return re.sub(r"\s+", " ", s.strip().lower())
    for kb_path in files:
        try:
            with open(kb_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                if isinstance(data, list):
                    for item in data:
                        en = item.get("English") or item.get("english") or item.get("source")
                        zh = item.get("Simplified_Chinese") or item.get("simplified_chinese") or item.get("Traditional_Chinese") or item.get("traditional_chinese") or item.get("target")
                        if en and zh:
                            key = normalize_key(en)
                            if key not in combined_kb:
                                combined_kb[key] = zh
        except Exception as e:
            logger.warning(f"KB load failed for {kb_path}: {e}")
    return combined_kb

def kb_lookup_fuzzy(key: str, cutoff=0.85):
    if not key:
        return None
    nk = re.sub(r"\s+", " ", key.strip().lower())
    if nk in KB_EN_TO_ZH:
        return KB_EN_TO_ZH[nk]
    matches = get_close_matches(nk, KB_EN_TO_ZH.keys(), n=1, cutoff=cutoff)
    if matches:
        return KB_EN_TO_ZH[matches[0]]
    return None

KB_EN_TO_ZH = load_combined_kb([
    "corrected_mandarine/corrected_mandarine.json",
    "corrected_mandarine/Traditional_manderin.json"
])

# -------------------------------
# NLTK Setup
# -------------------------------
def setup_nltk():
    try:
        nltk.data.find('tokenizers/punkt')
        return True
    except LookupError:
        try:
            nltk.download('punkt', quiet=True)
            nltk.data.find('tokenizers/punkt')
            return True
        except Exception as e:
            logger.warning("NLTK punkt download failed: %s", e)
            return False

nltk_available = setup_nltk()

def safe_sent_tokenize(text):
    if not text or not text.strip():
        return []
    if text.count("\n\n") > 3:
        parts = []
        for para in text.split("\n\n"):
            para = para.strip()
            if not para:
                continue
            if len(para) < 200:
                parts.append(para)
            else:
                if nltk_available:
                    from nltk.tokenize import sent_tokenize
                    parts.extend(sent_tokenize(para))
                else:
                    parts.extend(re.split(r'(?<=[\.\?\!])\s+', para))
        return [p.strip() for p in parts if p.strip()]
    if nltk_available:
        from nltk.tokenize import sent_tokenize
        try:
            return sent_tokenize(text)
        except Exception:
            return re.split(r'(?<=[\.\?\!])\s+', text)
    return re.split(r'(?<=[\.\?\!])\s+', text)

# -------------------------------
# Model Loading
# -------------------------------
@st.cache_resource
def load_translation_model():
    MODEL_NAME = "facebook/m2m100_418M"
    try:
        tokenizer = M2M100Tokenizer.from_pretrained(MODEL_NAME)
        model = M2M100ForConditionalGeneration.from_pretrained(MODEL_NAME)
        device = "cpu"
        model.to(device)
        return model, tokenizer, device
    except Exception as e:
        st.error(f"❌ Failed to load model on CPU: {e}")
        st.stop()

try:
    model, tokenizer, device = load_translation_model()
except Exception as e:
    st.error(f"Failed to load translation model: {e}")
    st.stop()

# -------------------------------
# Text Preprocessing
# -------------------------------
def clean_text(text):
    if not text:
        return text
    text = re.sub(r'\u00A0', ' ', text)
    text = re.sub(r'\s+', ' ', text).strip()
    text = re.sub(r'[\x00-\x08\x0b-\x0c\x0e-\x1f]', '', text)
    return text

def preserve_formatting_elements(text):
    preserved_elements = {}
    url_pattern = r'https?://[^\s]+'
    urls = re.findall(url_pattern, text)
    for i, url in enumerate(urls):
        placeholder = f"__URL_{i}__"
        text = text.replace(url, placeholder)
        preserved_elements[placeholder] = url
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, text)
    for i, email in enumerate(emails):
        placeholder = f"__EMAIL_{i}__"
        text = text.replace(email, placeholder)
        preserved_elements[placeholder] = email
    citation_pattern = r'\[[0-9]+\]'
    citations = re.findall(citation_pattern, text)
    for i, citation in enumerate(citations):
        placeholder = f"__CITATION_{i}__"
        text = text.replace(citation, placeholder)
        preserved_elements[placeholder] = citation
    return text, preserved_elements

def restore_formatting_elements(text, preserved_elements):
    for placeholder, original in preserved_elements.items():
        text = text.replace(placeholder, original)
    return text

# -------------------------------
# DOCX Reader
# -------------------------------
def read_docx_enhanced(file_path):
    try:
        doc = docx.Document(file_path)
        content = []
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content.append(paragraph.text.strip())
        for table in doc.tables:
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_data.append(cell.text.strip())
                if row_data:
                    content.append(" | ".join(row_data))
        return "\n\n".join(content)
    except Exception as e:
        logger.error(f"Error reading DOCX: {str(e)}")
        return ""

# -------------------------------
# Translation Functions
# -------------------------------
# (all the same translation functions from your original file remain here unchanged)

# -------------------------------
# Streamlit App
# -------------------------------
st.set_page_config(page_title="Perfect Document Translator", layout="wide")

st.title("📘 Perfect Document Translator with Exact Formatting Preservation")
st.write("Upload a DOCX document and translate it with CPU processing while preserving **exact** formatting.")

file = st.file_uploader("Upload Document", type=["docx"])

if file is not None:
    st.info(f"📄 **File:** {file.name} ({file.size} bytes")
    with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.name)[1]) as tmp:
        tmp.write(file.read())
        tmp_path = tmp.name

    try:
        with st.spinner("Extracting text from document..."):
            text = read_docx_enhanced(tmp_path)
            original_path_for_validation = tmp_path

        if not text or not text.strip():
            st.error("❌ Could not extract text from the document.")
            st.stop()

        st.success(f"✅ Successfully extracted {len(text)} characters")
        st.subheader("📑 Original Text Preview")
        st.text_area(
            label="Preview",
            value=text[:1000] + "..." if len(text) > 1000 else text,
            height=200,
            label_visibility="collapsed"
        )

        if st.button("🚀 Translate Document", type="primary"):
            status_text = st.empty()
            status_text.text("🔄 Translating DOCX with formatting preservation...")
            translated_path = translate_docx_xml_level(tmp_path, "zh")
            if translated_path:
                status_text.text("✅ Translation complete!")
                with open(translated_path, "rb") as f:
                    st.download_button(
                        label="📥 Download Translated Document",
                        data=f,
                        file_name=f"translated_{file.name}",
                        mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                    )
            else:
                st.error("❌ Translation failed")
    finally:
        try:
            os.unlink(tmp_path)
        except:
            pass
