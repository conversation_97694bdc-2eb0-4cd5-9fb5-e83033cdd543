# Streamlit PDF Translator - FULLY FIXED VERSION
# Fixed: st.subtitle error, use_column_width deprecation, SQLite database issues, document closed/encrypted errors
# Enhanced: Preserve formatting (bold, italic, font family, color, text fitting) in translated PDF
# Further Enhanced: Line-based extraction and translation, redaction, text wrapping and fitting
# Enhanced for Chinese: Use CJK fonts (china-s/sb) for target language Chinese
# Enhanced for Structure: Detect and preserve headings, lists, paragraphs, underlines, template boxes
# Further Fixed: Alignment, spacing, list preservation, overlap prevention, layout, incomplete translation, robust document handling

import streamlit as st
import tempfile
import os
import io
import logging
import json
import sqlite3
from pathlib import Path
from typing import Optional, Dict, List, Any
import numpy as np
import cv2
import re  # For list/heading detection

# Core PDF processing imports
import fitz  # PyMuPDF
from pdfminer.pdfdocument import PDFDocument
from pdfminer.pdfinterp import PDFResourceManager
from pdfminer.pdfpage import PDFPage
from pdfminer.pdfparser import PDFParser
from pdfminer.converter import PDFConverter
from pdfminer.layout import <PERSON><PERSON>har, LTFigure, LTLine, LTPage

# Hugging Face imports
from transformers import M2M100ForConditionalGeneration, M2M100Tokenizer
import torch

# Document layout analysis
try:
    import onnx
    import onnxruntime
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Model configuration
MODEL_NAME = "facebook/m2m100_418M"
SUPPORTED_LANGUAGES = {
    "English": "en",
    "Chinese (Simplified)": "zh",
    "Chinese (Traditional)": "zh",
    "Spanish": "es",
    "French": "fr",
    "German": "de",
    "Japanese": "ja",
    "Korean": "ko",
    "Russian": "ru",
    "Arabic": "ar",
    "Italian": "it",
    "Portuguese": "pt",
    "Dutch": "nl",
    "Hindi": "hi"
}

# Language code mapping for M2M100
M2M_LANG_MAP = {
    "en": "en", "zh": "zh", "zh": "zh",
    "es": "es", "fr": "fr", "de": "de",
    "ja": "ja", "ko": "ko", "ru": "ru",
    "ar": "ar", "it": "it", "pt": "pt",
    "nl": "nl", "hi": "hi"
}

class TranslationCache:
    """Robust SQLite-based translation cache with proper error handling"""

    def __init__(self, db_path: str = ":memory:"):
        self.db_path = db_path
        self.connection = None
        self.init_db()

    def get_connection(self):
        """Get database connection with proper error handling"""
        try:
            if self.connection is None:
                self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            return self.connection
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            self.connection = sqlite3.connect(":memory:", check_same_thread=False)
            self.init_db()
            return self.connection

    def init_db(self):
        """Initialize database with proper error handling"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                CREATE TABLE IF NOT EXISTS translations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    source_lang TEXT NOT NULL,
                    target_lang TEXT NOT NULL,
                    original_text TEXT NOT NULL,
                    translated_text TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(source_lang, target_lang, original_text) ON CONFLICT REPLACE
                )
            """)

            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_translation_lookup 
                ON translations(source_lang, target_lang, original_text)
            """)

            conn.commit()
            logger.info("Translation cache database initialized successfully")

        except Exception as e:
            logger.error(f"Database initialization error: {e}")
            self.cache_dict = {}

    def get(self, source_lang: str, target_lang: str, text: str) -> Optional[str]:
        """Get translation from cache with error handling"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT translated_text FROM translations WHERE source_lang=? AND target_lang=? AND original_text=?",
                (source_lang, target_lang, text)
            )
            result = cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            logger.warning(f"Cache get error: {e}")
            if hasattr(self, 'cache_dict'):
                key = f"{source_lang}:{target_lang}:{hash(text)}"
                return self.cache_dict.get(key)
            return None

    def set(self, source_lang: str, target_lang: str, original: str, translation: str):
        """Set translation in cache with error handling"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "INSERT OR REPLACE INTO translations (source_lang, target_lang, original_text, translated_text) VALUES (?, ?, ?, ?)",
                (source_lang, target_lang, original, translation)
            )
            conn.commit()
        except Exception as e:
            logger.warning(f"Cache set error: {e}")
            if not hasattr(self, 'cache_dict'):
                self.cache_dict = {}
            key = f"{source_lang}:{target_lang}:{hash(original)}"
            self.cache_dict[key] = translation

    def close(self):
        """Close database connection"""
        try:
            if self.connection:
                self.connection.close()
                self.connection = None
        except Exception as e:
            logger.warning(f"Error closing cache connection: {e}")

@st.cache_resource
def get_translation_cache():
    """Create a cached translation cache instance"""
    return TranslationCache()

class M2M100Translator:
    """M2M100-based translator with improved caching"""

    def __init__(self, model_name: str = MODEL_NAME):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.cache = get_translation_cache()
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    @st.cache_resource
    def load_model(_self):
        """Load M2M100 model and tokenizer"""
        try:
            if _self.model is None:
                with st.spinner("Loading M2M100 translation model..."):
                    _self.tokenizer = M2M100Tokenizer.from_pretrained(_self.model_name)
                    _self.model = M2M100ForConditionalGeneration.from_pretrained(_self.model_name)
                    _self.model.to(_self.device)
                    logger.info(f"Model loaded successfully on {_self.device}")
            return _self.model, _self.tokenizer
        except Exception as e:
            logger.error(f"Model loading error: {e}")
            st.error(f"Failed to load translation model: {e}")
            return None, None

    def translate(self, text: str, source_lang: str, target_lang: str) -> str:
        """Translate text using M2M100 with comprehensive error handling"""
        if not text.strip():
            return text

        try:
            cached = self.cache.get(source_lang, target_lang, text)
            if cached:
                return cached

            model, tokenizer = self.load_model()
            if model is None or tokenizer is None:
                logger.warning("Model not available, returning original text")
                return text

            tokenizer.src_lang = M2M_LANG_MAP.get(source_lang, source_lang)

            encoded = tokenizer(text, return_tensors="pt", max_length=2048, truncation=True).to(self.device)

            with torch.no_grad():
                generated_tokens = model.generate(
                    **encoded,
                    forced_bos_token_id=tokenizer.get_lang_id(M2M_LANG_MAP.get(target_lang, target_lang)),
                    max_new_tokens=2048,
                    num_beams=5,
                    early_stopping=True,
                    do_sample=False,
                    temperature=0.7
                )

            translation = tokenizer.decode(generated_tokens[0], skip_special_tokens=True)

            translation = translation.replace("?", "'").replace("·", "'").replace("C·", "C'").replace("L·", "L'")
            if re.match(r'^\d+\.\s', text):
                num_match = re.match(r'^(\d+\.)\s*(.*)', text)
                if num_match:
                    trans_text = self.translate(num_match.group(2), source_lang, target_lang)
                    translation = f"{num_match.group(1)} {trans_text}"

            self.cache.set(source_lang, target_lang, text, translation)

            return translation

        except Exception as e:
            logger.error(f"Translation error for text '{text[:50]}...': {e}")
            return text

class SimpleDocLayoutAnalyzer:
    """Simplified document layout analyzer without ONNX dependencies"""

    def __init__(self):
        self.stride = 32

    def predict(self, image: np.ndarray, imgsz: int = 1024) -> Dict:
        """Simple layout analysis based on image processing"""
        try:
            h, w = image.shape[:2]
            layout = np.zeros((h, w), dtype=int)
            layout.fill(0)
            return {"layout": layout, "boxes": [], "names": ["text", "figure", "table"]}
        except Exception as e:
            logger.warning(f"Layout analysis error: {e}")
            return {"layout": np.zeros((100, 100), dtype=int), "boxes": [], "names": ["text"]}

class PDFConverter:
    """Enhanced PDF converter with robust error handling and formatting preservation"""

    def __init__(self, translator: M2M100Translator, source_lang: str, target_lang: str, source_full: str, target_full: str):
        self.translator = translator
        self.source_lang = source_lang
        self.target_lang = target_lang
        self.source_full = source_full
        self.target_full = target_full
        self.layout_analyzer = SimpleDocLayoutAnalyzer()
        self.is_target_chinese = "Chinese" in self.target_full
        self.line_height = 14

    def detect_text_type(self, text: str, size: float, flags: int, is_bold: bool, block_type: int) -> str:
        """Detect if text is heading, list item, etc., with block type support"""
        text = text.strip()
        if re.match(r'^\d+\.', text) or re.match(r'^[-•*+]', text):
            return "list_item"
        if (size > 14 or is_bold) and block_type == 0:
            return "heading"
        if block_type == 0 and len(text.split()) < 10 and text.endswith('.'):
            return "paragraph"
        if block_type == 1:
            return "image"
        return "body"

    def get_fontname(self, base: str, bold: bool, italic: bool) -> str:
        """Map to base-14 font variant, with CJK support for Chinese target"""
        if self.is_target_chinese:
            if bold:
                return "china-sb"
            else:
                return "china-s"
        else:
            if base == "helv":
                if bold and italic: return "hebi"
                elif bold: return "hebo"
                elif italic: return "heit"
                else: return "helv"
            elif base == "tira":
                if bold and italic: return "tibi"
                elif bold: return "tibo"
                elif italic: return "tiit"
                else: return "tira"
            elif base == "cour":
                if bold and italic: return "cobi"
                elif bold: return "coob"
                elif italic: return "coit"
                else: return "cour"
            else:
                return "helv"

    def get_text_width(self, text: str, fontname: str, fontsize: float) -> float:
        """Calculate text width using font glyph advances"""
        try:
            font = fitz.Font(fontname=fontname)
            total = 0.0
            for c in text:
                code = ord(c)
                if code == 32:
                    gid, adv = font.glyph_advance(32, 0)
                else:
                    gid, adv = font.glyph_advance(code, 0)
                total += adv * (fontsize / 1000.0)
            return total
        except Exception as e:
            logger.warning(f"Text width calculation error: {e}")
            avg_width = 1.0 if any(ord(c) > 127 for c in text) else 0.6
            return len(text) * fontsize * avg_width

    def find_fitting_fontsize(self, text: str, fontname: str, bbox_width: float, initial_size: float) -> float:
        """Binary search for the largest fontsize that fits the text in the bbox width"""
        def width_at_size(s: float) -> float:
            return self.get_text_width(text, fontname, s)

        if width_at_size(initial_size) <= bbox_width * 0.95:
            return initial_size

        low, high = 1.0, initial_size
        for _ in range(20):
            mid = (low + high) / 2
            if width_at_size(mid) <= bbox_width * 0.95:
                low = mid
            else:
                high = mid
        return max(low, 4.0)

    def extract_text_with_coordinates(self, doc: fitz.Document) -> List[Dict]:
        """Block-based extraction for better structure preservation"""
        text_blocks = []
        avg_size = 12

        try:
            for page_num in range(doc.page_count):
                page = doc[page_num]
                blocks = page.get_text("dict")

                for block in blocks.get("blocks", []):
                    block_type = block.get("type", 0)
                    if block_type == 1:
                        continue
                    if "lines" in block:
                        for line in block["lines"]:
                            if line["spans"]:
                                line_text = ''.join(span["text"] for span in line["spans"]).strip()
                                if line_text and len(line_text) > 1:
                                    first_span = line["spans"][0]
                                    size = first_span.get("size", avg_size)
                                    flags = first_span.get("flags", 0)
                                    is_bold = bool(flags & 4)
                                    is_italic = bool(flags & 2)
                                    detected_type = self.detect_text_type(line_text, size, flags, is_bold, block_type)

                                    avg_size = (avg_size + size) / 2

                                    text_blocks.append({
                                        "page": page_num,
                                        "text": line_text,
                                        "bbox": fitz.Rect(line["bbox"]) if not isinstance(line["bbox"], fitz.Rect) else line["bbox"],
                                        "origin": first_span["origin"],
                                        "font": first_span.get("font", "Unknown"),
                                        "size": size,
                                        "flags": flags,
                                        "color": first_span.get("color", 0),
                                        "type": detected_type,
                                        "is_bold": is_bold,
                                        "is_italic": is_italic,
                                        "spans": line["spans"],
                                        "block_type": block_type
                                    })
        except Exception as e:
            logger.error(f"Text extraction error: {e}")
            st.error(f"Failed to extract text from PDF: {e}")

        return text_blocks

    def translate_text_lines(self, text_blocks: List[Dict]) -> List[Dict]:
        """Translate text blocks with progress tracking and error handling"""
        translated_blocks = []

        if not text_blocks:
            st.warning("No text blocks to translate")
            return translated_blocks

        progress_bar = st.progress(0)
        status_text = st.empty()
        total_blocks = len(text_blocks)

        try:
            for i, block in enumerate(text_blocks):
                status_text.text(f"Translating block {i+1}/{total_blocks}")

                text = block["text"]

                if self.is_formula(text) or len(text.strip()) < 2:
                    translated_text = text
                else:
                    translated_text = self.translator.translate(
                        text, self.source_lang, self.target_lang
                    )

                translated_block = block.copy()
                translated_block["translated_text"] = translated_text
                translated_blocks.append(translated_block)

                progress_bar.progress((i + 1) / total_blocks)
        except Exception as e:
            logger.error(f"Translation process error: {e}")
            st.error(f"Translation process failed: {e}")
        finally:
            progress_bar.empty()
            status_text.empty()

        return translated_blocks

    def is_formula(self, text: str) -> bool:
        """Enhanced formula detection"""
        if not text or len(text.strip()) < 1:
            return True

        formula_indicators = [
            "∫", "∑", "∏", "√", "∞", "±", "≤", "≥", "≠", "≈", "∈", "∉", "⊂", "⊃",
            "α", "β", "γ", "δ", "ε", "ζ", "η", "θ", "ι", "κ", "λ", "μ", "ν", "ξ",
            "π", "ρ", "σ", "τ", "υ", "φ", "χ", "ψ", "ω", "Γ", "Δ", "Θ", "Λ", "Π", "Σ", "Φ", "Ψ", "Ω"
        ]

        math_chars = ["²", "³", "⁴", "⁵", "⁶", "⁷", "⁸", "⁹", "₀", "₁", "₂", "₃", "₄", "₅", "₆", "₇", "₈", "₉"]

        for indicator in formula_indicators + math_chars:
            if indicator in text:
                return True

        if any(char in text for char in ["=", "+", "-", "*", "/", "^"]) and len(text.strip()) < 30:
            word_count = len(text.split())
            if word_count <= 3:
                return True

        if len(text.strip()) <= 2 and not text.strip().isalpha():
            return True

        return False

    def create_translated_pdf(self, original_doc: fitz.Document, translated_blocks: List[Dict]) -> fitz.Document:
        """Create translated PDF using redaction and enhanced textbox insertion"""
        new_doc = fitz.open()
        
        try:
            # Insert all pages from original document
            new_doc.insert_pdf(original_doc)
            
            if not translated_blocks:
                st.warning("No translated blocks to process")
                return new_doc

            page_blocks = {}
            for block in translated_blocks:
                page_num = block["page"]
                if page_num not in page_blocks:
                    page_blocks[page_num] = []
                page_blocks[page_num].append(block)

            progress_bar = st.progress(0)
            status_text = st.empty()

            for i, (page_num, blocks) in enumerate(page_blocks.items()):
                status_text.text(f"Processing page {page_num + 1}")

                if page_num < new_doc.page_count:
                    page = new_doc[page_num]

                    blocks.sort(key=lambda b: b["bbox"].y0 if hasattr(b["bbox"], "y0") else float('inf'), reverse=True)

                    redacts = []
                    insertions = []
                    prev_y = None

                    for block in blocks:
                        if (block["text"] != block["translated_text"] and not self.is_formula(block["text"])):
                            # Redact original text
                            for span in block["spans"]:
                                span_bbox = fitz.Rect(span["bbox"]) if not isinstance(span["bbox"], fitz.Rect) else span["bbox"]
                                redacts.append(page.add_redact_annot(span_bbox))

                            bbox = fitz.Rect(block["bbox"]) if not isinstance(block["bbox"], fitz.Rect) else block["bbox"]

                            orig_font = block["font"].lower()
                            if any(x in orig_font for x in ["courier", "monospace", "fixed"]):
                                base_family = "cour"
                            elif any(x in orig_font for x in ["times", "serif"]):
                                base_family = "tira"
                            else:
                                base_family = "helv"

                            is_bold = block["is_bold"] or block["type"] == "heading"
                            is_italic = block["is_italic"]
                            fontname = self.get_fontname(base_family, is_bold, is_italic)

                            color_int = block.get("color", 0)
                            col = (r / 255.0, g / 255.0, b / 255.0) if color_int != 0 and (r := (color_int >> 16) & 0xFF) and (g := (color_int >> 8) & 0xFF) and (b := color_int & 0xFF) else (0, 0, 0)

                            original_size = float(block["size"])
                            if block["type"] == "heading":
                                original_size *= 1.2
                            translated_text = block["translated_text"].strip()
                            if translated_text:
                                fitted_size = self.find_fitting_fontsize(translated_text, fontname, bbox.width, original_size)

                                indent = 20 if block["type"] == "list_item" else 0

                                y_offset = bbox.y0
                                if prev_y and block["type"] in ["paragraph", "body"]:
                                    spacing = self.line_height * 2
                                    y_offset = prev_y - spacing
                                prev_y = y_offset

                                adjusted_bbox = fitz.Rect(bbox.x0 + indent, y_offset, bbox.x1, bbox.y1 + (self.line_height if block["type"] == "paragraph" else 0))

                                insertions.append({
                                    "bbox": adjusted_bbox,
                                    "text": translated_text,
                                    "fontname": fontname,
                                    "fontsize": fitted_size,
                                    "color": col,
                                    "indent": indent,
                                    "type": block["type"]
                                })

                    # Apply redactions
                    if redacts:
                        page.apply_redactions(images=fitz.PDF_REDACT_IMAGE_NONE)

                    # Insert translated text
                    for ins in insertions:
                        if ins["type"] in ["paragraph", "body"]:
                            page.insert_textbox(
                                ins["bbox"],
                                ins["text"],
                                fontname=ins["fontname"],
                                fontsize=ins["fontsize"],
                                color=ins["color"],
                                align=0,
                                morph=True,
                                rotate=0
                            )
                        else:
                            point = fitz.Point(ins["bbox"].x0 + ins["indent"], ins["bbox"].y0)
                            page.insert_text(
                                point,
                                ins["text"],
                                fontsize=ins["fontsize"],
                                fontname=ins["fontname"],
                                color=ins["color"]
                            )

                progress_bar.progress((i + 1) / len(page_blocks))

            progress_bar.empty()
            status_text.empty()

            return new_doc

        except Exception as e:
            logger.error(f"PDF creation error: {e}")
            st.error(f"Failed to create translated PDF: {e}")
            return new_doc  # Return partially processed document
        finally:
            if not new_doc.is_closed:
                pass  # Keep document open for caller to handle

def main():
    """Main Streamlit application with all fixes applied"""
    st.set_page_config(
        page_title="PDF Translator with M2M100",
        page_icon="📄",
        layout="wide"
    )

    st.title("🌐 PDF Document Translator")
    st.markdown("### Powered by Facebook M2M100 and PDFMathTranslate Architecture")

    with st.sidebar:
        st.header("⚙️ Configuration")

        source_lang_full = st.selectbox(
            "Source Language",
            options=list(SUPPORTED_LANGUAGES.keys()),
            index=0
        )

        target_lang_full = st.selectbox(
            "Target Language",
            options=list(SUPPORTED_LANGUAGES.keys()),
            index=4
        )

        st.header("🔧 Advanced Options")
        preserve_formulas = st.checkbox("Preserve Mathematical Formulas", value=True)
        use_cache = st.checkbox("Use Translation Cache", value=True)
        preserve_structure = st.checkbox("Preserve Structure (Headings, Lists, Boxes)", value=True)

        st.header("📊 Model Information")
        st.info(f"""
        **Model**: {MODEL_NAME}
        **Type**: Multilingual Machine Translation
        **Languages**: {len(SUPPORTED_LANGUAGES)} supported
        **Device**: {'CUDA' if torch.cuda.is_available() else 'CPU'}
        **CJK Font Support**: {'Enabled' if 'Chinese' in target_lang_full else 'N/A'}
        **Structure Preservation**: {'Enabled' if preserve_structure else 'Disabled'}
        """)

    col1, col2 = st.columns([1, 1])

    with col1:
        st.header("📄 Upload PDF")

        uploaded_file = st.file_uploader(
            "Choose a PDF file",
            type=["pdf"],
            help="Upload a PDF document to translate"
        )

        password = None
        if uploaded_file is not None:
            st.success(f"File uploaded: {uploaded_file.name}")

            file_size = len(uploaded_file.getvalue()) / (1024 * 1024)
            st.write(f"**File Size**: {file_size:.2f} MB")

            # File size limit
            MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
            if len(uploaded_file.getvalue()) > MAX_FILE_SIZE:
                st.error("📁 File size exceeds 50MB limit.")
                return

            with st.expander("📖 Preview Original PDF"):
                try:
                    with fitz.open(stream=uploaded_file.getvalue(), filetype="pdf") as doc:
                        if doc.is_encrypted:
                            st.warning("This PDF is encrypted. Please provide a password.")
                            for attempt in range(3):
                                password = st.text_input(f"Enter PDF Password (Attempt {attempt + 1}/3)", type="password", key=f"pass_{attempt}")
                                if password:
                                    doc.authenticate(password)
                                    if not doc.needs_pass:
                                        st.success("Password accepted. Previewing PDF...")
                                        break
                                    else:
                                        st.error(f"Incorrect password. {2 - attempt} attempts remaining.")
                                if attempt == 2:
                                    st.error("Maximum password attempts exceeded. Please upload a non-encrypted PDF or provide the correct password.")
                                    return
                        if not doc.is_closed and not doc.is_encrypted:
                            first_page = doc[0]
                            pix = first_page.get_pixmap(matrix=fitz.Matrix(1.5, 1.5))
                            img_data = pix.tobytes("png")
                            st.image(img_data, caption="First Page Preview", use_container_width=True)
                except Exception as e:
                    st.error(f"Preview error: {e}")
                    logger.error(f"Preview error: {e}")

    with col2:
        st.header("🚀 Translation")

        if uploaded_file is not None:
            if st.button("🌟 Translate PDF", type="primary"):
                try:
                    if source_lang_full == target_lang_full:
                        st.warning("⚠️ Source and target languages are the same. Please select different languages.")
                        return

                    translator = M2M100Translator()
                    source_code = SUPPORTED_LANGUAGES[source_lang_full]
                    target_code = SUPPORTED_LANGUAGES[target_lang_full]

                    converter = PDFConverter(translator, source_code, target_code, source_lang_full, target_lang_full)

                    with st.spinner("Processing PDF..."):
                        # Use BytesIO to handle the uploaded file properly
                        pdf_bytes = uploaded_file.getvalue()
                        doc = fitz.open(stream=pdf_bytes, filetype="pdf")

                        # Handle encrypted PDFs
                        if doc.is_encrypted:
                            if not password:
                                st.warning("🔒 This PDF is encrypted. Please provide a password in the preview section.")
                                doc.close()
                                return
                            if not doc.authenticate(password):
                                st.error("❌ Incorrect password or decryption failed.")
                                doc.close()
                                return

                        # Check if document is valid
                        if doc.page_count == 0:
                            st.error("📄 The PDF appears to be empty or corrupted.")
                            doc.close()
                            return

                        st.write("📝 Extracting text blocks...")
                        text_blocks = converter.extract_text_with_coordinates(doc)

                        if len(text_blocks) == 0:
                            st.warning("⚠️ No text found in the PDF. The document might be image-based or empty.")
                            doc.close()
                            return

                        st.write(f"Found {len(text_blocks)} text blocks")

                        st.write("🌐 Translating text...")
                        translated_blocks = converter.translate_text_lines(text_blocks)

                        if not translated_blocks:
                            st.error("❌ Translation failed - no blocks were processed")
                            doc.close()
                            return

                        st.write("📄 Creating translated PDF...")
                        translated_doc = converter.create_translated_pdf(doc, translated_blocks)
                        translated_bytes = translated_doc.tobytes()

                        doc.close()  # Close original document
                        translated_doc.close()  # Close translated document

                    st.success("✅ Translation completed!")

                    st.header("📋 Translation Results")

                    col_stats1, col_stats2, col_stats3 = st.columns(3)
                    with col_stats1:
                        st.metric("Text Blocks", len(text_blocks))
                    with col_stats2:
                        translated_count = sum(1 for b in translated_blocks if b["text"] != b["translated_text"])
                        st.metric("Translated Blocks", translated_count)
                    with col_stats3:
                        unique_pages = len(set(b["page"] for b in text_blocks))
                        st.metric("Pages", unique_pages)

                    filename = uploaded_file.name.replace('.pdf', f'_translated_{target_lang_full.lower().replace(" ", "_").replace("(", "").replace(")", "")}.pdf')
                    st.download_button(
                        label="📥 Download Translated PDF",
                        data=translated_bytes,
                        file_name=filename,
                        mime="application/pdf",
                        type="primary"
                    )

                    with st.expander("🔍 View Translation Sample"):
                        sample_blocks = [b for b in translated_blocks[:20] if b["text"] != b["translated_text"]]

                        if sample_blocks:
                            for i, block in enumerate(sample_blocks[:5]):
                                st.write(f"**Translation {i+1} ({block['type']}): **")
                                st.write(f"**Original**: {block['text'][:200]}...")
                                st.write(f"**Translated**: {block['translated_text'][:200]}...")
                                st.write("---")
                        else:
                            st.info("No translations to display. This might be due to formula preservation or text filtering.")

                except (fitz.FileDataError, ValueError) as e:
                    st.error(f"❌ Translation failed: {str(e)}. The PDF may be encrypted or corrupted.")
                    logger.error(f"Translation error: {e}, Document state - Closed: {doc.is_closed if 'doc' in locals() else 'N/A'}, Encrypted: {doc.is_encrypted if 'doc' in locals() else 'N/A'}")
                except Exception as e:
                    st.error(f"❌ Translation failed: {str(e)}")
                    logger.error(f"Translation error: {e}", exc_info=True)
        else:
            st.info("👆 Please upload a PDF file to begin translation")

    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666;'>
        <p>Built with ❤️ using Streamlit, Hugging Face Transformers, and PyMuPDF</p>
        <p>Inspired by <a href="https://github.com/Byaidu/PDFMathTranslate" target="_blank">PDFMathTranslate</a> project</p>
    </div>
    """, unsafe_allow_html=True)

    with st.sidebar:
        st.markdown("---")
        st.header("🐛 Debug Info")
        if st.checkbox("Show Debug Information"):
            st.write(f"**Streamlit Version**: {st.__version__}")
            st.write(f"**PyTorch Version**: {torch.__version__}")
            st.write(f"**Device**: {torch.device('cuda' if torch.cuda.is_available() else 'cpu')}")
            st.write(f"**CUDA Available**: {torch.cuda.is_available()}")
            if torch.cuda.is_available():
                st.write(f"**CUDA Device Count**: {torch.cuda.device_count()}")
                st.write(f"**CUDA Device Name**: {torch.cuda.get_device_name(0)}")

if __name__ == "__main__":